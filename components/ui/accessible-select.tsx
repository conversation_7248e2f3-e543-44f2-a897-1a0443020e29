import React, { useMemo } from "react";
import Select, { components, Props as SelectProps } from "react-select";

// Custom components with proper accessibility attributes
const AccessibleDropdownIndicator = (props: any) => (
  <components.DropdownIndicator {...props}>
    <div 
      role="button" 
      tabIndex={-1} 
      aria-label="Open dropdown menu"
      aria-expanded={props.selectProps.menuIsOpen}
    >
      {props.children}
    </div>
  </components.DropdownIndicator>
);

const AccessibleOption = (props: any) => (
  <components.Option {...props}>
    <div role="option" aria-selected={props.isSelected}>
      {props.children}
    </div>
  </components.Option>
);

const AccessibleSingleValue = (props: any) => (
  <components.SingleValue {...props}>
    <div role="textbox" aria-readonly="true">
      {props.children}
    </div>
  </components.SingleValue>
);

const AccessibleMultiValue = (props: any) => (
  <components.MultiValue {...props}>
    <div role="button" tabIndex={-1} aria-label={`Remove ${props.data.label}`}>
      {props.children}
    </div>
  </components.MultiValue>
);

const AccessibleControl = (props: any) => (
  <components.Control {...props}>
    <div 
      role="combobox" 
      aria-expanded={props.selectProps.menuIsOpen}
      aria-haspopup="listbox"
      aria-label={props.selectProps['aria-label'] || 'Select option'}
    >
      {props.children}
    </div>
  </components.Control>
);

interface AccessibleSelectProps extends SelectProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
}

const AccessibleSelect: React.FC<AccessibleSelectProps> = (props) => {
  // Memoize custom components to prevent recreation
  const customComponents = useMemo(() => ({
    DropdownIndicator: AccessibleDropdownIndicator,
    Option: AccessibleOption,
    SingleValue: AccessibleSingleValue,
    MultiValue: AccessibleMultiValue,
    Control: AccessibleControl,
    ...props.components, // Allow overriding with custom components
  }), [props.components]);

  // Enhanced styles for better accessibility
  const accessibleStyles = useMemo(() => ({
    control: (base: any, state: any) => ({
      ...base,
      // Ensure proper focus styles
      outline: state.isFocused ? '2px solid hsl(var(--primary))' : 'none',
      outlineOffset: '2px',
      borderColor: state.isFocused ? 'hsl(var(--primary))' : base.borderColor,
      boxShadow: 'none', // Remove default box-shadow to use outline
      ...props.styles?.control?.(base, state),
    }),
    option: (base: any, state: any) => ({
      ...base,
      // Ensure proper contrast for selected/focused options
      backgroundColor: state.isSelected 
        ? 'hsl(var(--primary))' 
        : state.isFocused 
        ? 'hsl(var(--muted))' 
        : base.backgroundColor,
      color: state.isSelected 
        ? 'hsl(var(--primary-foreground))' 
        : base.color,
      cursor: 'pointer',
      ...props.styles?.option?.(base, state),
    }),
    menu: (base: any) => ({
      ...base,
      zIndex: 9999, // Ensure menu appears above other elements
      ...props.styles?.menu?.(base),
    }),
    ...props.styles,
  }), [props.styles]);

  return (
    <Select
      {...props}
      components={customComponents}
      styles={accessibleStyles}
      aria-label={props['aria-label'] || 'Select option'}
      classNamePrefix="react-select"
      // Ensure keyboard navigation works properly
      tabSelectsValue={true}
      openMenuOnFocus={true}
      closeMenuOnSelect={!props.isMulti}
      // Add ARIA attributes for better screen reader support
      aria-describedby={props['aria-describedby']}
    />
  );
};

export default AccessibleSelect;
